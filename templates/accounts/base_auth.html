{% extends 'base.html' %}

{% block extra_css %}
{% load static %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Authentication Base */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;
        --cw-neutral-200: #e5e5e5;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

        /* Shadows */
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-card-subtle: linear-gradient(135deg, #ffffff 0%, var(--cw-accent-light) 100%);
    }

    /* Global Styles */
    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: white;
        min-height: 100vh;
    }

    h1, h2, h3, h4, h5, h6 {
        font-family: var(--cw-font-heading);
        font-weight: 600;
        color: var(--cw-brand-primary);
        line-height: 1.3;
    }

    /* Authentication Section */
    .auth-section {
        padding: 5rem 0;
        background: white;
        min-height: 100vh;
        display: flex;
        align-items: center;
    }

    .auth-container {
        max-width: 520px;
        width: 100%;
        margin: 0 auto;
        padding: 0 2rem;
    }

    .auth-card {
        background: white;
        border: 1px solid rgba(250, 225, 215, 0.3);
        border-radius: 1rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        width: 100%;
    }

    /* Wide card variant for signup forms */
    .auth-card.auth-card-wide {
        max-width: 600px;
    }

    /* Form Section */
    .auth-form-section {
        padding: 3rem;
    }

    /* Form Controls */
    .form-floating > label {
        color: var(--cw-neutral-600);
        font-weight: 500;
    }

    .form-control {
        border: 2px solid var(--cw-neutral-200);
        border-radius: 0.5rem;
        padding: 1rem;
        font-size: 1rem;
        color: var(--cw-neutral-800);
        background-color: white;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
        outline: none;
        background-color: white;
    }

    .form-control::placeholder {
        color: var(--cw-neutral-600);
    }

    /* Error state styling */
    .form-control.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    /* Button styling */
    .btn-primary {
        background: var(--cw-brand-primary);
        border: 2px solid var(--cw-brand-primary);
        color: white;
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        width: 100%;
    }

    .btn-primary:hover {
        background: var(--cw-brand-light);
        border-color: var(--cw-brand-light);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    /* Secondary button styling */
    .btn-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        border-radius: 0.5rem;
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        letter-spacing: 0.025em;
        width: 100%;
    }

    .btn-secondary:hover {
        background: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
    }

    /* Links */
    a {
        color: var(--cw-brand-primary);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    a:hover {
        color: var(--cw-brand-light);
        text-decoration: underline;
    }

    /* Form text (help text) styling */
    .form-text {
        margin-top: 0.5rem;
        font-size: 0.9rem;
        color: var(--cw-neutral-600);
    }

    /* Checkbox styling */
    .form-check-input {
        border: 2px solid var(--cw-neutral-200);
    }

    .form-check-input:checked {
        background-color: var(--cw-brand-primary);
        border-color: var(--cw-brand-primary);
    }

    .form-check-input:focus {
        border-color: var(--cw-brand-primary);
        box-shadow: 0 0 0 3px rgba(47, 22, 15, 0.1);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .auth-container {
            max-width: 100%;
            padding: 0 1rem;
        }
        
        .auth-card.auth-card-wide {
            max-width: 100%;
        }
    }

    @media (max-width: 576px) {
        .auth-section {
            padding: 2rem 0;
        }

        .auth-form-section {
            padding: 2rem;
        }
    }

    /* Additional component styles */
    {% block auth_extra_css %}{% endblock %}
</style>
{% endblock %}

{% block content %}
<section class="auth-section">
    <div class="auth-container">
        <div class="auth-card {% block auth_card_class %}{% endblock %}">
            {% block auth_header %}{% endblock %}
            
            <div class="auth-form-section">
                {% block auth_messages %}
                    {% include 'accounts/components/messages.html' %}
                {% endblock %}
                
                {% block auth_content %}{% endblock %}
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
{% block auth_extra_js %}{% endblock %}
{% endblock %}
