{% extends 'accounts/base_auth.html' %}

{% block title %}Sign In - CozyWish{% endblock %}

{% block auth_header %}
    {% include 'accounts/components/auth_header.html' with icon='fas fa-user' title='Welcome Back' subtitle='Sign in to your CozyWish account' %}
{% endblock %}

{% block auth_content %}
    <!-- Form Errors -->
    {% include 'accounts/components/form_errors.html' with form=form %}

    <!-- Login Form -->
    <form method="post" action="{% url 'account_login' %}" novalidate>
        {% csrf_token %}

        <!-- Email Field -->
        {% include 'accounts/components/form_field.html' with field=form.login field_type='email' placeholder='Enter your email address' autocomplete='email' %}

        <!-- Password Field -->
        {% include 'accounts/components/form_field.html' with field=form.password field_type='password' placeholder='Enter your password' autocomplete='current-password' %}

        <!-- Remember Me -->
        {% if form.remember %}
            {% include 'accounts/components/form_field.html' with field=form.remember %}
        {% endif %}

        <!-- Submit Button -->
        <button type="submit" class="btn btn-primary mb-3">
            <i class="fas fa-sign-in-alt me-2"></i>
            Sign In
        </button>

        <!-- Social Login -->
        {% include 'accounts/components/social_login.html' %}

        <!-- Additional Links -->
        <div class="text-center">
            <p class="mb-2">
                <a href="{% url 'account_reset_password' %}">Forgot your password?</a>
            </p>
            <p class="mb-0">
                Don't have an account?
                <a href="{% url 'account_signup' %}">Sign up here</a>
            </p>
        </div>
    </form>

    <!-- Footer -->
    {% include 'accounts/components/auth_footer.html' with footer_style='minimal' %}
{% endblock %}
