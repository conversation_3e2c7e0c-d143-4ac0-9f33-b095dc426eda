{% extends 'accounts/base_auth.html' %}

{% block title %}Create Account - CozyWish{% endblock %}

{% block auth_card_class %}auth-card-wide{% endblock %}

{% block auth_header %}
    {% include 'accounts/components/auth_header.html' with icon='fas fa-user-plus' title='Join <PERSON>ish' subtitle='Create your account and start discovering amazing venues' %}
{% endblock %}
{% block auth_extra_css %}
<style>
    /* Role selection styling */
    .role-selection {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .role-selection h6 {
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block auth_content %}
    <!-- Form Errors -->
    {% include 'accounts/components/form_errors.html' with form=form %}

    <!-- Signup Form -->
    <form method="post" action="{% url 'account_signup' %}" novalidate>
        {% csrf_token %}

        <!-- Role Selection (if available) -->
        {% if form.role %}
            <div class="role-selection">
                <h6>{{ form.role.label }}</h6>
                {% for choice in form.role %}
                    <div class="form-check mb-2">
                        {{ choice.tag }}
                        <label class="form-check-label" for="{{ choice.id_for_label }}">
                            {{ choice.choice_label }}
                        </label>
                    </div>
                {% endfor %}
                {% if form.role.help_text %}
                    <div class="form-text">{{ form.role.help_text }}</div>
                {% endif %}
            </div>
        {% endif %}

        <!-- Email Field -->
        {% include 'accounts/components/form_field.html' with field=form.email field_type='email' placeholder='Enter your email address' autocomplete='email' %}

        <!-- Password Fields -->
        {% include 'accounts/components/form_field.html' with field=form.password1 field_type='password' placeholder='Create a password' autocomplete='new-password' %}

        {% include 'accounts/components/form_field.html' with field=form.password2 field_type='password' placeholder='Confirm your password' autocomplete='new-password' %}

        <!-- Additional form fields (if any) -->
        {% for field in form %}
            {% if field.name not in "email,password1,password2,role" %}
                {% include 'accounts/components/form_field.html' with field=field %}
            {% endif %}
        {% endfor %}

        <!-- Submit Button -->
        <button type="submit" class="btn btn-primary mb-3">
            <i class="fas fa-user-plus me-2"></i>
            Create Account
        </button>

        <!-- Social Login -->
        {% include 'accounts/components/social_login.html' %}

        <!-- Additional Links -->
        <div class="text-center">
            <p class="mb-0">
                Already have an account?
                <a href="{% url 'account_login' %}">Sign in here</a>
            </p>
        </div>
    </form>

    <!-- Footer -->
    {% include 'accounts/components/auth_footer.html' with footer_style='minimal' %}
{% endblock %}
